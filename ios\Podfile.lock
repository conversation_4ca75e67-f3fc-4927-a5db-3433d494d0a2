PODS:
  - abseil/algorithm (1.20220623.0):
    - abseil/algorithm/algorithm (= 1.20220623.0)
    - abseil/algorithm/container (= 1.20220623.0)
  - abseil/algorithm/algorithm (1.20220623.0):
    - abseil/base/config
  - abseil/algorithm/container (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base (1.20220623.0):
    - abseil/base/atomic_hook (= 1.20220623.0)
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/base_internal (= 1.20220623.0)
    - abseil/base/config (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/base/dynamic_annotations (= 1.20220623.0)
    - abseil/base/endian (= 1.20220623.0)
    - abseil/base/errno_saver (= 1.20220623.0)
    - abseil/base/fast_type_id (= 1.20220623.0)
    - abseil/base/log_severity (= 1.20220623.0)
    - abseil/base/malloc_internal (= 1.20220623.0)
    - abseil/base/prefetch (= 1.20220623.0)
    - abseil/base/pretty_function (= 1.20220623.0)
    - abseil/base/raw_logging_internal (= 1.20220623.0)
    - abseil/base/spinlock_wait (= 1.20220623.0)
    - abseil/base/strerror (= 1.20220623.0)
    - abseil/base/throw_delegate (= 1.20220623.0)
  - abseil/base/atomic_hook (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20220623.0)
  - abseil/base/core_headers (1.20220623.0):
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/errno_saver (1.20220623.0):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20220623.0):
    - abseil/base/config
  - abseil/base/log_severity (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/prefetch (1.20220623.0):
    - abseil/base/config
  - abseil/base/pretty_function (1.20220623.0)
  - abseil/base/raw_logging_internal (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/cleanup/cleanup (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
  - abseil/cleanup/cleanup_internal (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
  - abseil/container/common (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/compressed_tuple (1.20220623.0):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20220623.0):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20220623.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/flat_hash_set (1.20220623.0):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20220623.0):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20220623.0):
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20220623.0):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/container/inlined_vector (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
  - abseil/container/inlined_vector_internal (1.20220623.0):
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20220623.0):
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/debugging/debugging_internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/functional/any_invocable (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/bind_front (1.20220623.0):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/hash/city (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20220623.0):
    - abseil/base/config
    - abseil/base/endian
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/memory (1.20220623.0):
    - abseil/memory/memory (= 1.20220623.0)
  - abseil/memory/memory (1.20220623.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20220623.0):
    - abseil/meta/type_traits (= 1.20220623.0)
  - abseil/meta/type_traits (1.20220623.0):
    - abseil/base/config
  - abseil/numeric/bits (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20220623.0):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/distributions (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20220623.0):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/fastmath (1.20220623.0):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20220623.0):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20220623.0):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20220623.0):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20220623.0):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20220623.0):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20220623.0):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20220623.0):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20220623.0):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20220623.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/random/internal/uniform_helper (1.20220623.0):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20220623.0):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20220623.0):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20220623.0):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20220623.0):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/status/statusor (1.20220623.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/cord (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/cord_internal (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20220623.0):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20220623.0):
    - abseil/base/config
  - abseil/strings/internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20220623.0):
    - abseil/strings/str_format_internal
  - abseil/strings/str_format_internal (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
  - abseil/strings/strings (1.20220623.0):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/internal
  - abseil/synchronization/graphcycles_internal (1.20220623.0):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20220623.0):
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20220623.0):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20220623.0):
    - abseil/time/internal (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
  - abseil/time/internal (1.20220623.0):
    - abseil/time/internal/cctz (= 1.20220623.0)
  - abseil/time/internal/cctz (1.20220623.0):
    - abseil/time/internal/cctz/civil_time (= 1.20220623.0)
    - abseil/time/internal/cctz/time_zone (= 1.20220623.0)
  - abseil/time/internal/cctz/civil_time (1.20220623.0):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20220623.0):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20220623.0):
    - abseil/base/base
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
  - abseil/types (1.20220623.0):
    - abseil/types/any (= 1.20220623.0)
    - abseil/types/bad_any_cast (= 1.20220623.0)
    - abseil/types/bad_any_cast_impl (= 1.20220623.0)
    - abseil/types/bad_optional_access (= 1.20220623.0)
    - abseil/types/bad_variant_access (= 1.20220623.0)
    - abseil/types/compare (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
  - abseil/types/any (1.20220623.0):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20220623.0):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20220623.0):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20220623.0):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20220623.0):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20220623.0):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - BoringSSL-GRPC (0.0.24):
    - BoringSSL-GRPC/Implementation (= 0.0.24)
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Implementation (0.0.24):
    - BoringSSL-GRPC/Interface (= 0.0.24)
  - BoringSSL-GRPC/Interface (0.0.24)
  - cloud_firestore (4.14.0):
    - Firebase/Firestore (= 10.18.0)
    - firebase_core
    - Flutter
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Firebase/CoreOnly (10.18.0):
    - FirebaseCore (= 10.18.0)
  - Firebase/Firestore (10.18.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.18.0)
  - firebase_core (2.24.2):
    - Firebase/CoreOnly (= 10.18.0)
    - Flutter
  - FirebaseAppCheckInterop (10.21.0)
  - FirebaseCore (10.18.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.21.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.21.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.18.0):
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseFirestoreInternal (~> 10.17)
    - FirebaseSharedSwift (~> 10.0)
  - FirebaseFirestoreInternal (10.21.0):
    - abseil/algorithm (~> 1.20220623.0)
    - abseil/base (~> 1.20220623.0)
    - abseil/container/flat_hash_map (~> 1.20220623.0)
    - abseil/memory (~> 1.20220623.0)
    - abseil/meta (~> 1.20220623.0)
    - abseil/strings/strings (~> 1.20220623.0)
    - abseil/time (~> 1.20220623.0)
    - abseil/types (~> 1.20220623.0)
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.49.1)"
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseSharedSwift (10.21.0)
  - Flutter (1.0.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - GoogleMaps (< 9.0)
  - GoogleMaps (6.2.1):
    - GoogleMaps/Maps (= 6.2.1)
  - GoogleMaps/Base (6.2.1)
  - GoogleMaps/Maps (6.2.1):
    - GoogleMaps/Base
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - "gRPC-C++ (1.49.1)":
    - "gRPC-C++/Implementation (= 1.49.1)"
    - "gRPC-C++/Interface (= 1.49.1)"
  - "gRPC-C++/Implementation (1.49.1)":
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/cleanup/cleanup (= 1.20220623.0)
    - abseil/container/flat_hash_map (= 1.20220623.0)
    - abseil/container/flat_hash_set (= 1.20220623.0)
    - abseil/container/inlined_vector (= 1.20220623.0)
    - abseil/functional/any_invocable (= 1.20220623.0)
    - abseil/functional/bind_front (= 1.20220623.0)
    - abseil/functional/function_ref (= 1.20220623.0)
    - abseil/hash/hash (= 1.20220623.0)
    - abseil/memory/memory (= 1.20220623.0)
    - abseil/meta/type_traits (= 1.20220623.0)
    - abseil/random/random (= 1.20220623.0)
    - abseil/status/status (= 1.20220623.0)
    - abseil/status/statusor (= 1.20220623.0)
    - abseil/strings/cord (= 1.20220623.0)
    - abseil/strings/str_format (= 1.20220623.0)
    - abseil/strings/strings (= 1.20220623.0)
    - abseil/synchronization/synchronization (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
    - abseil/utility/utility (= 1.20220623.0)
    - "gRPC-C++/Interface (= 1.49.1)"
    - gRPC-Core (= 1.49.1)
  - "gRPC-C++/Interface (1.49.1)"
  - gRPC-Core (1.49.1):
    - gRPC-Core/Implementation (= 1.49.1)
    - gRPC-Core/Interface (= 1.49.1)
  - gRPC-Core/Implementation (1.49.1):
    - abseil/base/base (= 1.20220623.0)
    - abseil/base/core_headers (= 1.20220623.0)
    - abseil/container/flat_hash_map (= 1.20220623.0)
    - abseil/container/flat_hash_set (= 1.20220623.0)
    - abseil/container/inlined_vector (= 1.20220623.0)
    - abseil/functional/any_invocable (= 1.20220623.0)
    - abseil/functional/bind_front (= 1.20220623.0)
    - abseil/functional/function_ref (= 1.20220623.0)
    - abseil/hash/hash (= 1.20220623.0)
    - abseil/memory/memory (= 1.20220623.0)
    - abseil/meta/type_traits (= 1.20220623.0)
    - abseil/random/random (= 1.20220623.0)
    - abseil/status/status (= 1.20220623.0)
    - abseil/status/statusor (= 1.20220623.0)
    - abseil/strings/cord (= 1.20220623.0)
    - abseil/strings/str_format (= 1.20220623.0)
    - abseil/strings/strings (= 1.20220623.0)
    - abseil/synchronization/synchronization (= 1.20220623.0)
    - abseil/time/time (= 1.20220623.0)
    - abseil/types/optional (= 1.20220623.0)
    - abseil/types/span (= 1.20220623.0)
    - abseil/types/variant (= 1.20220623.0)
    - abseil/utility/utility (= 1.20220623.0)
    - BoringSSL-GRPC (= 0.0.24)
    - gRPC-Core/Interface (= 1.49.1)
  - gRPC-Core/Interface (1.49.1)
  - leveldb-library (1.22.3)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)

DEPENDENCIES:
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)

SPEC REPOS:
  trunk:
    - abseil
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAppCheckInterop
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseSharedSwift
    - GoogleMaps
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - leveldb-library
    - nanopb
    - PromisesObjC

EXTERNAL SOURCES:
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"

SPEC CHECKSUMS:
  abseil: 926fb7a82dc6d2b8e1f2ed7f3a718bce691d1e46
  BoringSSL-GRPC: 3175b25143e648463a56daeaaa499c6cb86dad33
  cloud_firestore: 73eece22ce25a0565238c283ee9990f1618d8063
  Firebase: 414ad272f8d02dfbf12662a9d43f4bba9bec2a06
  firebase_core: 0af4a2b24f62071f9bf283691c0ee41556dcb3f5
  FirebaseAppCheckInterop: 69fc7d8f6a1cbfa973efb8d1723651de30d12525
  FirebaseCore: 2322423314d92f946219c8791674d2f3345b598f
  FirebaseCoreExtension: 1c044fd46e95036cccb29134757c499613f3f564
  FirebaseCoreInternal: 43c1788eaeee9d1b97caaa751af567ce11010d00
  FirebaseFirestore: 171bcbb57a1a348dd171a0d5e382c03ef85a77bb
  FirebaseFirestoreInternal: 7ac1e0c5b4e75aeb898dfe4b1d6d77abbac9eca3
  FirebaseSharedSwift: 19b3f709993d6fa1d84941d41c01e3c4c11eab93
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  google_maps_flutter_ios: f135b968a67c05679e0a53538e900b5c174b0d99
  GoogleMaps: 20d7b12be49a14287f797e88e0e31bc4156aaeb4
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  "gRPC-C++": 2df8cba576898bdacd29f0266d5236fa0e26ba6a
  gRPC-Core: a21a60aefc08c68c247b439a9ef97174b0c54f96
  leveldb-library: e74c27d8fbd22854db7cb467968a0b8aa1db7126
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.15.2
